<template>
    <div class="audio-player">
        <div class="player-content">
            <!-- 播放/暂停按钮 -->
            <a-tooltip :title="playStatus ? '暂停' : '播放'">
                <button class="play-toggle" @click="togglePlay" aria-label="播放控制">
                    <svg v-if="!playStatus" viewBox="0 0 24 24" width="24" height="24">
                        <path d="M8 5v14l11-7z" fill="currentColor" />
                    </svg>
                    <svg v-else viewBox="0 0 24 24" width="24" height="24">
                        <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" fill="currentColor" />
                    </svg>
                </button>
            </a-tooltip>

            <!-- 线性进度条模式 -->
            <div v-if="progressType === 'line'" class="line-progress">
                <a-slider
                    v-model:value="currentProgress"
                    @change="seekToPosition"
                    :step="0.1"
                    class="progress-slider"
                />
                <div class="time">
                    {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
                </div>
            </div>

            <!-- 波形可视化模式 -->
            <div v-if="progressType === 'wave'" class="wave-visualizer">
                <div
                    v-for="(_, index) in WAVE_COUNT"
                    :key="index"
                    class="line-item"
                    :style="{ height: getWaveHeight(index) + 'px' }"
                />
            </div>

            <!-- 其他控制按钮 -->
            <div class="other-controller">
                <div v-if="progressType === 'wave'" class="time">
                    {{ formatTime(currentTime) }}
                </div>

                <a-tooltip v-if="restart" title="重新播放">
                    <button class="control-btn" @click="restartAudio" aria-label="重新播放">
                        <ReloadOutlined />
                    </button>
                </a-tooltip>

                <a-tooltip v-if="download" title="下载音频">
                    <button class="control-btn" @click="downloadAudio" aria-label="下载音频">
                        <DownloadOutlined />
                    </button>
                </a-tooltip>
            </div>
        </div>

        <!-- 音频元素 -->
        <audio
            ref="audioElement"
            :src="audioUrl"
            @loadedmetadata="onAudioLoaded"
            @timeupdate="onTimeUpdate"
            @ended="onAudioEnded"
        />
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, onUnmounted, computed } from 'vue'
import { Tooltip as ATooltip, Slider as ASlider } from 'ant-design-vue'
import { ReloadOutlined, DownloadOutlined } from '@ant-design/icons-vue'

// 类型定义
interface AudioPlayerProps {
    audioUrl: string
    progressType: 'line' | 'wave'
    download: boolean
    restart: boolean
}

// 常量定义
const WAVE_COUNT = 60 // 波形条数量
const AUDIO_CONFIG = {
    FFT_SIZE: 256,
    SMOOTHING_TIME_CONSTANT: 0.8,
    MIN_HEIGHT: 8,
    MAX_HEIGHT: 40,
    BASE_HEIGHT: 8,
    MAX_ADDITIONAL_HEIGHT: 32
} as const

const props = withDefaults(defineProps<AudioPlayerProps>(), {
    audioUrl: '',
    progressType: 'line',
    download: true,
    restart: true
})

// 响应式状态
const audioElement = ref<HTMLAudioElement | null>(null)
const currentProgress = ref<number>(0)
const playStatus = ref<boolean>(false)
const currentTime = ref<number>(0)
const duration = ref<number>(0)

// 音频分析相关
const audioContext = ref<AudioContext | null>(null)
const analyser = ref<AnalyserNode | null>(null)
const source = ref<MediaElementAudioSourceNode | null>(null)
const animationId = ref<number | null>(null)
const frequencyData = ref<Uint8Array | null>(null)

// 波形数据
const waveRealtimeHeights = ref<number[]>([])
const waveStaticHeights = ref<number[]>([])

// 计算属性
const isWaveMode = computed(() => props.progressType === 'wave')

// 初始化波形高度数组
const initWaveHeights = () => {
    const heights: number[] = []
    const center = Math.floor(WAVE_COUNT / 2)

    for (let i = 0; i < WAVE_COUNT; i++) {
        const distanceFromCenter = Math.abs(i - center)
        const maxDistance = Math.floor(WAVE_COUNT / 2)
        const decayFactor = Math.cos((distanceFromCenter / maxDistance) * (Math.PI / 2))
        const randomVariation = (Math.random() - 0.5) * 4
        const maxHeight = 20
        const height = Math.max(
            AUDIO_CONFIG.MIN_HEIGHT,
            Math.min(28, AUDIO_CONFIG.BASE_HEIGHT + (maxHeight * decayFactor) + randomVariation)
        )
        heights.push(height)
    }
    waveStaticHeights.value = heights
    waveRealtimeHeights.value = [...heights]
}

// 初始化音频分析
const initRealtimeAnalysis = () => {
    if (!audioElement.value) return

    try {
        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext
        audioContext.value = new AudioContextClass()
        analyser.value = audioContext.value.createAnalyser()
        analyser.value.fftSize = AUDIO_CONFIG.FFT_SIZE
        analyser.value.smoothingTimeConstant = AUDIO_CONFIG.SMOOTHING_TIME_CONSTANT
        source.value = audioContext.value.createMediaElementSource(audioElement.value)

        source.value.connect(analyser.value)
        analyser.value.connect(audioContext.value.destination)

        const bufferLength = analyser.value.frequencyBinCount
        frequencyData.value = new Uint8Array(bufferLength)
    } catch (error) {
        console.warn('音频分析初始化失败:', error)
    }
}

// 分析音频数据并更新波形
const analyzeRealtimeAudio = () => {
    if (!analyser.value || !frequencyData.value) return

    analyser.value.getByteFrequencyData(frequencyData.value)

    // 计算音频强度
    const totalSum = frequencyData.value.reduce((sum, value) => sum + value, 0)
    const normalizedIntensity = (totalSum / frequencyData.value.length) / 255

    const center = Math.floor(WAVE_COUNT / 2)
    const newHeights: number[] = []

    for (let i = 0; i < WAVE_COUNT; i++) {
        const distanceFromCenter = Math.abs(i - center)
        const maxDistance = Math.floor(WAVE_COUNT / 2)
        const decayFactor = Math.cos((distanceFromCenter / maxDistance) * (Math.PI / 2))

        const height = Math.max(
            AUDIO_CONFIG.MIN_HEIGHT,
            Math.min(
                AUDIO_CONFIG.MAX_HEIGHT,
                AUDIO_CONFIG.BASE_HEIGHT + (normalizedIntensity * AUDIO_CONFIG.MAX_ADDITIONAL_HEIGHT * decayFactor)
            )
        )
        newHeights.push(height)
    }
    waveRealtimeHeights.value = newHeights

    if (playStatus.value) {
        animationId.value = requestAnimationFrame(analyzeRealtimeAudio)
    }
}

// 停止音频分析
const stopRealtimeAnalysis = () => {
    if (animationId.value) {
        cancelAnimationFrame(animationId.value)
        animationId.value = null
    }
    // 恢复静态波形
    if (isWaveMode.value) {
        waveRealtimeHeights.value = [...waveStaticHeights.value]
    }
}

// 启动音频分析
const startRealtimeAnalysis = () => {
    if (!isWaveMode.value) return

    if (!audioContext.value) {
        initRealtimeAnalysis()
    }

    if (audioContext.value?.state === 'suspended') {
        audioContext.value.resume()
    }

    analyzeRealtimeAudio()
}

// 播放/暂停切换
const togglePlay = () => {
    if (!audioElement.value || !props.audioUrl) return;

    if (playStatus.value) {
        // console.log('暂停');
        audioElement.value.pause();
        playStatus.value = false;
        if (props.progressType === 'wave') {
            stopRealtimeAnalysis();
        }
    } else {
        // console.log('播放');
        audioElement.value.play();
        playStatus.value = true;
        if (props.progressType === 'wave') {
            if (!audioContext.value) {
                initRealtimeAnalysis();
            }
            // 确保音频上下文在播放前是“running”状态
            if (audioContext.value?.state === 'suspended') {
                audioContext.value.resume();
            }
            analyzeRealtimeAudio(); // 启动循环
        }
    }
};

// 暂停音频（外部调用）
const pauseAudio = () => {
    if (!audioElement.value) return
    audioElement.value.pause()
    playStatus.value = false
    stopRealtimeAnalysis()
}

// 音频加载完成
const onAudioLoaded = () => {
    if (!audioElement.value) return

    const rawDuration = audioElement.value.duration
    if (isNaN(rawDuration) || !isFinite(rawDuration) || rawDuration <= 0) {
        // 等待 canplay 事件获取有效 duration
        const handleCanPlay = () => {
            if (audioElement.value && isFinite(audioElement.value.duration) && audioElement.value.duration > 0) {
                duration.value = audioElement.value.duration
            }
            audioElement.value?.removeEventListener('canplay', handleCanPlay)
        }
        audioElement.value.addEventListener('canplay', handleCanPlay)
    } else {
        duration.value = rawDuration
    }

    // 初始化波形
    if (isWaveMode.value) {
        initWaveHeights()
    }
}

// 音频时间更新
const onTimeUpdate = () => {
    if (!audioElement.value) return

    currentTime.value = audioElement.value.currentTime

    // 动态更新 duration
    if ((duration.value <= 0 || !isFinite(duration.value)) &&
        isFinite(audioElement.value.duration) && audioElement.value.duration > 0) {
        duration.value = audioElement.value.duration
    }

    if (duration.value > 0) {
        currentProgress.value = (audioElement.value.currentTime / duration.value) * 100
    }
}

// 音频播放结束
const onAudioEnded = () => {
    playStatus.value = false
    currentProgress.value = 0
    currentTime.value = 0
    stopRealtimeAnalysis()
}

// 跳转到指定位置
const seekToPosition = (value: number | [number, number]) => {
    if (!audioElement.value || !duration.value) return
    const targetValue = Array.isArray(value) ? value[0] : value
    const targetTime = (targetValue / 100) * duration.value
    audioElement.value.currentTime = targetTime
}

// 重新播放
const restartAudio = () => {
    if (!audioElement.value) return

    audioElement.value.currentTime = 0
    currentProgress.value = 0
    currentTime.value = 0

    if (!playStatus.value) {
        audioElement.value.play()
        playStatus.value = true
        startRealtimeAnalysis()
    }
}

// 时间格式化
const formatTime = (seconds: number): string => {
    if (isNaN(seconds) || !isFinite(seconds) || seconds < 0) {
        return '00:00'
    }
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 下载音频
const downloadAudio = () => {
    if (!props.audioUrl) return

    try {
        const link = document.createElement('a')
        link.href = props.audioUrl
        link.download = `语音合成_${Date.now()}.mp3`
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    } catch (error) {
        console.warn('下载失败:', error)
        window.open(props.audioUrl, '_blank')
    }
}

// 获取波形高度
const getWaveHeight = (index: number): number => {
    if (playStatus.value && waveRealtimeHeights.value.length > 0) {
        return waveRealtimeHeights.value[index] || AUDIO_CONFIG.MIN_HEIGHT
    }
    return waveStaticHeights.value[index] || AUDIO_CONFIG.MIN_HEIGHT
}

// 重置音频状态
const resetAudioState = () => {
    playStatus.value = false
    currentProgress.value = 0
    currentTime.value = 0
    duration.value = 0
    stopRealtimeAnalysis()
}

// 组件挂载
onMounted(() => {
    if (isWaveMode.value) {
        initWaveHeights()
    }
})

// 监听 audioUrl 变化
watch(() => props.audioUrl, (newUrl) => {
    if (newUrl) {
        resetAudioState()
        if (isWaveMode.value) {
            initWaveHeights()
        }
    }
})

// 组件卸载清理
onUnmounted(() => {
    stopRealtimeAnalysis()
    if (audioContext.value) {
        audioContext.value.close()
        audioContext.value = null
        analyser.value = null
        source.value = null
    }
})

defineExpose({
    pauseAudio
});
</script>

<style lang="scss">
.audio-player {
    width: 100%;

    .player-content {
        margin: 0 auto;
        height: 48px;
        background: var(--player-btg);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        animation: player_an 0.5s ease-out forwards;
        overflow: hidden; // **关键：防止内部元素溢出父容器**

        .play-toggle {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            border: none;
            margin: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            flex-shrink: 0;
            transition: all 0.2s ease;

            &:hover {
                transform: scale(1.1);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            }

            svg {
                width: 24px;
                height: 24px;
            }
        }

        .line-progress {
            display: flex;
            align-items: center;
            flex: 1;
            gap: 12px;

            .progress-slider {
                flex: 1;
                margin: 0;

                :deep(.ant-slider-track) {
                    background-color: var(--primary-color);
                }

                :deep(.ant-slider-handle) {
                    border-color: var(--primary-color);
                }

                :deep(.ant-slider-track:hover) {
                    background-color: var(--primary-color);
                }
            }
        }

        .wave-visualizer {
            display: flex;
            align-items: center;
            flex: 1;
            justify-content: space-between;
            padding: 0 5px;
            height: 100%;

            .line-item {
                flex-shrink: 0;
                width: 2px;
                min-height: 8px;
                max-height: 40px;
                background: var(--primary-color);
                margin: 0 0.5px;
                border-radius: 8px;
                transition: height 0.1s ease-out;
                opacity: 0.8;

                &.playing {
                    box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
                    opacity: 1;
                }
            }
        }

        .time {
            font-size: 12px;
            color: var(--text-color-secondary, #666);
            white-space: nowrap;
            min-width: 60px;
            text-align: center;
            flex-shrink: 0;
        }

        .other-controller {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 10px;
            flex-shrink: 0;

            .control-btn {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                background: var(--bg-color-light, #f0f0f0);
                color: var(--text-color-secondary, #666);
                border: none;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                    background: var(--bg-color-hover, #e0e0e0);
                    color: var(--primary-color);
                    transform: scale(1.1);
                }

                svg {
                    width: 16px;
                    height: 16px;
                }
            }
        }
    }

    // 响应式设计
    @media screen and (max-width: 768px) {
        .player-content {
            padding: 0 5px;
        }

        .play-toggle {
            margin: 0 5px;
        }

        .wave-visualizer {
            padding: 0 2px;

            .line-item {
                width: 1px !important;
                margin: 0 0.5px !important;
            }
        }

        .time {
            min-width: 50px;
            font-size: 11px;
        }

        .other-controller {
            margin: 0 5px;
            gap: 5px;
        }
    }

    @keyframes player_an {
        0% {
            opacity: 0;
            transform: scale(0.4);
        }

        100% {
            opacity: 1;
            transform: scale(1);
        }
    }
}
</style>
