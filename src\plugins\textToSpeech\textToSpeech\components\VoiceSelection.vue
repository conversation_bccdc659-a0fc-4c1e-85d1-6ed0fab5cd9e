<template>
  <div class="voice-selection">
    <div class="selector-title">选择音色</div>
    <div class="source-tabs" ref="tabsContainer">
      <div class="tab-item" :class="{ active: activeTab === 'local' }" @click="switchTab('local')" ref="localTab">
        公共音色
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'online' }" @click="switchTab('online')" ref="onlineTab">
        我的音色
      </div>
      <!-- 滑动指示器 -->
      <div class="tab-indicator" :style="indicatorStyle"></div>
      <div class="clone-voice" @click="cloneModal = true">
        <Button type="other" width="120px" height="35px">
          <div class="btn-con">
            复刻音色
            <PlusCircleOutlined />
          </div>
        </Button>
      </div>
    </div>
    <a-spin :spinning="loading" class="spin-style">
      <div class="voice-list" v-if="currentVoiceList.length > 0">
        <div :class="['voice-item', item.id == activeVicoe ? 'active' : '']" v-for="(item,index) in currentVoiceList"
          :key="item.id" @click.stop="selectVoice(item)">
          <div class="item-top">
            <img :src="item.avatar" />
            <div class="pause-btn" v-if="item.id == cureentPlay" @click.stop="onPauseVoiceItem">
              <PauseCircleOutlined />
            </div>
            <div v-else class="play-btn" @click.stop="onPlayVoiceItem(item)">
              <PlayCircleOutlined />
            </div>
          </div>
          <div class="item-bottom">
            <div class="voice-name" :dir="textDir(item.name )">{{ item.name }}</div>
          </div>
          <div class="more" v-if="activeTab == 'online'">
            <aDropDown :trigger="['click']" placement="bottom" :arrow="{ pointAtCenter: true }" @click.stop>
              <span>...</span>
              <template #overlay>
                <a-menu @click="onMore(item.id,index)">
                  <a-menu-item key="1" style="color: red;">
                    <DeleteOutlined /> 删除
                  </a-menu-item>
                </a-menu>
              </template>
            </aDropDown>
          </div>
        </div>
      </div>
      <div class="empty" v-else>
        <img src="../assets/empty.svg" alt="" />
        <div class="empty-title">您还没有复刻音色</div>
      </div>
    </a-spin>
    <CloneCom v-if="cloneModal" @close="closeModal" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, computed, nextTick, createVNode } from "vue";
import {
  Spin as aSpin,
  Dropdown as aDropDown,
  MenuItem as aMenuItem,
  Menu as aMenu,
  Modal,
  message
} from "ant-design-vue";
import { textToSpeechSerApi } from "../server/server"; //api使用方式
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  PlusCircleOutlined
} from "@ant-design/icons-vue";
import {textDir} from '../utils/utils'
import Button from "./Button.vue";
import type { Voice, VoiceListResponse } from "../types";
import CloneCom from "./CloneCom.vue";
const textToSpeechApi = textToSpeechSerApi();
const emit = defineEmits(["selectVoice"]);
// 音色列表
const voiceList = ref<VoiceListResponse | null>(null);

// 当前音色列表
const currentVoiceList = ref<Voice[]>([]);
const cloneModal = ref<boolean>(false);

// tabs
const activeTab = ref<string>("local");
const tabsContainer = ref<HTMLElement>();
const localTab = ref<HTMLElement>();
const onlineTab = ref<HTMLElement>();

// 创建audio元素
const audioElement = ref<HTMLAudioElement | null>(null);

// 当前音色
const activeVicoe = ref<number>(0);

// 当前试听播放
const cureentPlay = ref<number>(0);

// loading状态
const loading = ref<boolean>(true);
onMounted(() => {
  activeTab.value = "local";
  // 确保DOM渲染完成后再计算位置
  nextTick(() => {
    // 触发重新计算
    if (tabsContainer.value) {
      tabsContainer.value.offsetHeight;
    }
  });
  getclone_list();
  initAudio();
});

// 选择音色
const selectVoice = (item: Voice) => {
  activeVicoe.value = item.id;
  emit("selectVoice", item);
};
// 音色试听播放
const onPlayVoiceItem = (item: Voice) => {
  cureentPlay.value = item.id;
  playAudio(item);
};
// 音色试听停放
const onPauseVoiceItem = () => {
  cureentPlay.value = 0;
  pauseAudio();
};

//获取音色列表
const getclone_list = (index?:number|undefined) => {
  loading.value = true;
  textToSpeechApi
    .getclone_list()
    .then((res: any) => {
      voiceList.value = res;
      
      if (activeTab.value == 'local') {
        currentVoiceList.value = res.public_clone_list || [];
        selectVoice(res.public_clone_list[index?index-1:0]);
      } else {
        currentVoiceList.value = res.user_clone_list || [];
        selectVoice(res.user_clone_list[index?index-1:0]);
      }

    })
    .finally(() => {
      loading.value = false;
    });
};

// 初始化音频
const initAudio = () => {
  audioElement.value = new Audio();
  // 事件监听
  audioElement.value.addEventListener("loadedmetadata", handleLoadedMetadata);
  audioElement.value.addEventListener("timeupdate", handleTimeUpdate);
  audioElement.value.addEventListener("ended", handleEnded);
  audioElement.value.addEventListener("canplay", handleCanPlay);
};

// / 元数据加载完成
const handleLoadedMetadata = () => {
  // if (audioElement.value) {
  //   duration.value = audioElement.value.duration;
  // }
};

// 可以播放时
const handleCanPlay = () => { };

// 播放时间更新
const handleTimeUpdate = () => {
  if (audioElement.value) {
  }
};

// 播放结束
const handleEnded = () => {
  cureentPlay.value = 0;
};

// 播放音频
const playAudio = async (item: Voice) => {
  if (audioElement.value) {
    try {
      audioElement.value.src = item.audio_url;
      await audioElement.value.play();
    } catch (error) {
      console.error("播放失败:", error);
    }
  }
};

// 暂停音频
const pauseAudio = () => {
  if (audioElement.value) {
    audioElement.value.pause();
  }
};

// 计算指示器的样式
const indicatorStyle = computed(() => {
  if (!tabsContainer.value || !localTab.value || !onlineTab.value) {
    return {
      opacity: "0",
    };
  }

  const activeTabElement =
    activeTab.value === "local" ? localTab.value : onlineTab.value;
  const containerRect = tabsContainer.value.getBoundingClientRect();
  const tabRect = activeTabElement.getBoundingClientRect();

  // 计算相对于容器的位置
  const left = tabRect.left - containerRect.left;
  const width = tabRect.width * 0.8; // 80% 宽度
  const leftOffset = left + (tabRect.width - width) / 2; // 居中对齐

  return {
    transform: `translateX(${leftOffset}px)`,
    width: `${width}px`,
    opacity: "1",
  };
});
const onMore = (id: number,index:number) => {
    console.log('itemID:', id);
    Modal.confirm({
      title: '你确定删除该音色吗？',
      icon: createVNode(ExclamationCircleOutlined),
      content: '删除音色后无法恢复，如需要该音色要您重新复刻音色！',
      cancelText: '取消',
      okText: '确定',
      okType: 'danger',
      onOk() {
        return new Promise((resolve, reject) => {
          textToSpeechApi.delete_voice(id)
            .then(res => {
              console.log(res);
              getclone_list(index)
              resolve(res);
              message.success('删除成功');
            })
            .catch(err => {
              console.log(err);
              reject(err);
              message.error('删除失败');
            });
        }).catch(() => {
          console.log('Oops errors!');
        });
      },
      onCancel() { },
    })
  
}

const switchTab = (tab: string) => {
  activeTab.value = tab;
  if (tab === "local") {
    currentVoiceList.value = voiceList.value?.public_clone_list || [];
  } else {
    currentVoiceList.value = voiceList.value?.user_clone_list || [];
  }
};
const closeModal = (type: any) => {
  if (type == 'success') {
    getclone_list()
    cloneModal.value = false
  } else {
    cloneModal.value = false
  }
}

</script>

<style lang="scss">
.voice-selection {
  width: 60%;
  margin: 0 auto;
  min-height: 533px;
  padding: 24px;
  border: 1px solid var(--border-color);
  border-radius: 24px;
  background: var(--card-bg);

  .selector-title {
    font-size: 18px;
    color: var(--text-color);
  }

  .spin-style {
    background: rgb(249 249 249 / 31%);
    backdrop-filter: blur(5px);
  }

  .source-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    position: relative;

    .tab-item {
      padding: 8px 16px;
      cursor: pointer;
      color: var(--text-color);
      position: relative;
      text-align: center;
      margin-top: 20px;
      transition: color 0.3s ease;
      padding-bottom: 15px;

      &.active {
        color: var(--primary-color);
      }

      &:hover {
        color: var(--primary-color);
      }
    }

    .tab-indicator {
      position: absolute;
      bottom: 0;
      height: 3px;
      background-color: var(--primary-color);
      border-radius: 8px;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      opacity: 0;
    }

    .clone-voice {
      position: absolute;
      right: 20px;
      font-size: 14px;

      // top: calc(50%);
      .btn-con {
        // width: 90px;
        // display: flex;
        // align-items: center;
        // justify-content: space-between;
      }
    }
  }

  .voice-list {
    display: flex;
    flex-wrap: wrap;
    padding: 10px 0;
    max-height: 400px;
    overflow-y: scroll;

    .voice-item {
      width: 183px;
      height: 140px;
      padding: 10px 0;
      border: 2px solid var(--border-color);
      border-radius: 24px;
      margin: 10px;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--text-color);
      position: relative;

      .item-top {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #edf6ff;
        border-radius: 50%;
        overflow: hidden;
        margin-bottom: 15px;
        position: relative;

        img {
          width: 100%;
          height: 100%;
        }

        .play-btn,
        .pause-btn {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          bottom: 0;
          background: #00000048;
          color: white;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 32px;
        }

        .play-btn {
          display: none;
        }

        &:hover {
          .play-btn {
            display: flex;
          }
        }
      }

      .item-bottom {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 500;
        position: relative;

        .voice-name {
          margin-bottom: 5px;
        }

        .voice-create-date {
          font-size: 12px;
          color: #999;
        }
      }

      &:hover {
        background: var(--hover-bg);
      }

      &.active {
        border-color: var(--active-border);
        background: var(--active-bg);
        color: var(--active-text);

        .item-top {
          background: #bfdfff;
        }
      }

      .more {
        width: 24px;
        height: 24px;
        position: absolute;
        right: 10px;
        bottom: 10px;
        background: #ebebeb;
        color: rgb(0, 0, 0);
        font-size: 22px;
        border-radius: 50%;

        span {
          position: absolute;
          top: -7px;
          left: 5px;
        }
      }
    }
  }

  .empty {
    width: 100%;
    min-height: 400px;
    text-align: center;
    padding: 20px 0;

    .empty-title {
      margin-top: 15px;
      color: var(--text-color);
      font-size: 14px;
    }

    &>img {
      width: 280px;
      height: 250px;
    }
  }
}

@media screen and (max-width: 1500px) {
  .voice-selection {
    width: 100%;
    padding: 15px;

    .voice-list {
      .voice-item {
        width: 140px;
        height: 130px;
        margin: 5px;
        padding: 0;
      }
    }
  }

  .empty {
    img {
      width: 200px;
      height: 220px;
    }
  }
}
</style>